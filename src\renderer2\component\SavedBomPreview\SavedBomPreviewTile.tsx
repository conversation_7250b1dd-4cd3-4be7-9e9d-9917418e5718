import React, { useEffect } from 'react'

import styles from './SavedBomPreview.module.scss'
import clsx from 'clsx';
import { ReactComponent as DomesticCheckTextIcon } from '../../assets/New-images/Domestic-Check-Text.svg';
import { ReactComponent as RemoveLineIcon } from '../../assets/New-images/remove-line-icon.svg';
import { formatDollarPerUnit, formatToTwoDecimalPlaces, priceUnits } from '@bryzos/giss-ui-library';

const SavedBomPreviewTile = ({ item, index, fields, watch, getValues, calculateMaterialTotalPrice, isRemoveLineBtnClicked, handleRemoveLineBtnClick }: any) => {
    const dollerPerUmFormatter = (umVal: any) => {
        const price_unit = getValues(`cart_items.${index}.price_unit`);
        return formatDollarPerUnit(price_unit, umVal, index);
    }
    function display(data) {
        const description = data?.UI_Description || watch(`cart_items.${index}.description`); 
        const lines = description.split("\n");
        const firstLine = lines[0];
        const restLines = lines.slice(1);
    
        return (
          <>
            <p className={clsx('liFisrtLine', styles.liFisrtLine)}>{firstLine}</p>
            {restLines.map((line, index) => (
              <p key={index}>{line}</p>
            ))}
          </>
        );
      }

    return (
        <tr key={index} className={styles.marginBottom} >
            <td>
                <div className={styles.prodId}>
                    <div className={styles.domesticMaterialCheckbox} >
                        <label className={styles.lineNumberContainer}>
                            <span
                                className={clsx(
                                    styles.customNumberToggle,
                                    watch(`cart_items.${index}.domesticMaterialOnly`) ? styles.active : "",
                                    ((!(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0)) || watch(`cart_items.${index}.lineStatus`) === 'SKIPPED') ? styles.disabled : "",
                                    watch(`cart_items.${index}.lineStatus`) === 'SKIPPED' ? styles.domesticSkipDisabled : ""
                                )}
                                role="checkbox"
                                id={"domesticMaterialCheckbox" + index}
                                aria-checked={watch(`cart_items.${index}.domesticMaterialOnly`) ?? false}
                                defaultChecked={watch(`cart_items.${index}.domesticMaterialOnly`) ?? false}
                                aria-disabled={((!(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0)) || watch(`cart_items.${index}.lineStatus`) === 'SKIPPED')}
                            >
                                {index + 1}
                            </span>
                            <span className={clsx(styles.usaOnlyText, watch(`cart_items.${index}.domesticMaterialOnly`) ? "" : styles.visibilityHidden)}>USA<br />ONLY</span>
                        </label>
                    </div>
                    {isRemoveLineBtnClicked && (
                        <div className={styles.removeLineIconContainer}>
                            <button className={styles.removeLineIcon} onClick={()=>{handleRemoveLineBtnClick(index)}}><RemoveLineIcon /></button>
                        </div>
                    )}
                </div>
            </td>
            {!(watch(`cart_items.${index}.lineStatus`) && watch(`cart_items.${index}.lineStatus`) === 'SKIPPED') ?
                (
                    <>
                        <td>
                            <span className={clsx(styles.poDescription, (watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.length > 0) && styles.poDescriptionFirstLine,
                                (watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.split("\n")[0]?.includes('Miscellaneous')) && styles.poDescriptionFirstLine1
                                )} >
                                    
                                {display(watch(`cart_items.${index}.descriptionObj`))}
                                {watch(`cart_items.${index}.product_tag`) ?? ""}
                            </span>
                        </td>
                        <td>
                            <div className={styles.priceAndUnit}>
                                <div>
                                    {watch(`cart_items.${index}.qty`) ? formatToTwoDecimalPlaces(watch(`cart_items.${index}.qty`)) : ''}
                                </div>
                                {watch(`cart_items.${index}.qty_unit`) &&
                                    <span className={styles.selectUom}>
                                        {watch(`cart_items.${index}.qty_unit`).toUpperCase()}
                                    </span>
                                }
                            </div>
                        </td>
                        <td>
                            <div className={styles.priceAndUnit}>
                                <div>
                                    {!!(watch(`cart_items.${index}.price`)) &&
                                        dollerPerUmFormatter(watch(`cart_items.${index}.price`) ?? 0)
                                    }
                                </div>
                                {watch(`cart_items.${index}.price_unit`) &&
                                    <span className={styles.selectUom1}>
                                        {watch(`cart_items.${index}.price_unit`).toUpperCase()}
                                    </span>
                                }
                            </div>
                        </td>
                        <td>
                            <div className={styles.extendedValue}>
                                {!!(watch(`cart_items.${index}.extended`)) &&
                                    formatToTwoDecimalPlaces(watch(`cart_items.${index}.extended`) ?? 0)
                                }
                            </div>
                        </td>
                    </>
                ) : (
                    <td colSpan={5} className={styles.skippedLineTd}>
                        <div className={styles.skippedLineContent}>
                            <span>Placeholder (manually skipped on entry)</span>
                        </div>
                    </td>
                )
            }

        </tr>
    )
}

export default SavedBomPreviewTile
