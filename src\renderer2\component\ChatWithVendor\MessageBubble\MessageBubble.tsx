import React, { useState } from 'react';
import { Tooltip } from '@mui/material';
import styles from './MessageBubble.module.scss';
import clsx from 'clsx';

// Helper: extract links from text and split string into parts
const parseLinks = (text: string) => {
  const urlRegex = /(https?:\/\/[^\s<]+)|(www\.[^\s<]+)/gi;
  const elements: (string | { url: string })[] = [];
  let lastIndex = 0;

  let match: RegExpExecArray | null;
  while ((match = urlRegex.exec(text)) !== null) {
    // Push text before the link
    if (match.index > lastIndex) {
      elements.push(text.substring(lastIndex, match.index));
    }
    // Push the link
    elements.push({ url: match[0] });
    lastIndex = match.index + match[0].length;
  }
  // Push any remaining text
  if (lastIndex < text.length) {
    elements.push(text.substring(lastIndex));
  }
  return elements;
};

// Microlink Preview Component for Tooltip Content
const LinkPreviewContent: React.FC<{ url: string }> = ({ url }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  React.useEffect(() => {
    let cancelled = false;
    setLoading(true);
    fetch(`https://api.microlink.io/?url=${encodeURIComponent(url)}`)
      .then(res => res.json())
      .then(res => {
        if (!cancelled) {
          setData(res.data);
          setLoading(false);
        }
      });
    return () => { cancelled = true; };
  }, [url]);

  if (loading) {
    return <div className=''>Loading preview...</div>;
  }
  if (!data) return null;
  return (
    <div style={{ maxWidth: 300 }}>
      {data.image && (
        <img
          src={data.image.url || data.image}
          alt=""
          style={{
            width: '100%',
            maxHeight: 80,
            objectFit: 'contain',
            borderRadius: 6,
            marginBottom: 8
          }}
        />
      )}
      <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{data.title}</div>
      <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>{data.description}</div>
      <div style={{ fontSize: 11, color: '#999' }}>{data.url}</div>
    </div>
  );
};

// Main MessageBubble
interface MessageBubbleProps {
  text: string;
  isMyMessage: boolean;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ text, isMyMessage }) => {
  // Replace links with React <a> tags wrapped in MUI Tooltips
  const content = parseLinks(text).map((part, i) =>
    typeof part === 'string' ? (
      <React.Fragment key={i}>{part}</React.Fragment>
    ) : (
      <Tooltip
        key={i}
        title={<LinkPreviewContent url={part.url.startsWith('http') ? part.url : 'http://' + part.url} />}
        placement="bottom-start"
        disableInteractive={false}
        classes={{
          tooltip: styles.linkTooltip,
        }}
      >
        <a
          href={part.url.startsWith('http') ? part.url : 'http://' + part.url}
          target="_blank"
          rel="noopener noreferrer"
          style={{ color: '#fff', textDecoration: 'underline' }}
        >
          {part.url}
        </a>
      </Tooltip>
    )
  );

  return (
    <div
      className={clsx(
        styles.messageBubble,
        !isMyMessage && styles.othersMessage
      )}
    >
      {content}
    </div>
  );
};

export default MessageBubble;
