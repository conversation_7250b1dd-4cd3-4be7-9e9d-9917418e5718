.attachmentBubble {
    padding: 8px 8px 8px 8px;
    border-radius: 6px;
    background-color: #000;
    color: #ffffff;
    max-width: 70%;
    font-family: Inter;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.24;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    word-wrap: break-word;
    min-width: 200px;
    padding: 10px;
  }
  
  .othersMessage {
    background-color: #d4d4d4;
    color: #000;
  }
  
  .attachmentContent {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-family: Inter;
  }
  
  .attachmentInfo {
    margin-left: 10px;
    overflow: hidden;
    display: flex;
    align-items: center;
  }
  
  .attachmentName {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
  }
  
  .attachmentExtension {
    font-size: 12px;
  }
  
  .attachmentDownload {
    display: block;
    text-align: right;
    font-size: 12px;
    color: inherit;
    text-decoration: underline;
    margin-top: 4px;
    
    &:hover {
      opacity: 0.8;
    }
  }
  
  // File type icons (placeholders)
  .documentIcon, .imageIcon, .spreadsheetIcon, 
  .presentationIcon, .archiveIcon, .unknownIcon {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
  }
  
  .documentIcon {
    background-color: #4285F4;
  }
  
  .imageIcon {
    background-color: #0F9D58;
    svg{
      path{
        fill: #fff;
      }
    }
  }
  
  .spreadsheetIcon {
    background-color: #F4B400;
  }
  
  .presentationIcon {
    background-color: #DB4437;
  }
  
  .archiveIcon {
    background-color: #673AB7;
  }
  
  .unknownIcon {
    background-color: #757575;
  }

  .imageTooltip {
    padding: 8px;
    max-width: 300px;
    background-color: rgba(0, 0, 0, 0.85);
    border-radius: 8px;
  }

  .imagePreviewContainer {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .imagePreview {
    max-width: 100%;
    max-height: 200px;
    object-fit: contain;
    border-radius: 4px;
  }

  .imagePreviewInfo {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .imagePreviewName {
    font-weight: bold;
    font-size: 14px;
    color: #fff;
    word-break: break-word;
  }

  .imagePreviewType {
    font-size: 12px;
    color: #ccc;
  }
