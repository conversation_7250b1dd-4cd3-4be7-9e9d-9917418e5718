// @ts-nocheck
import { nativeTheme, Notification } from 'electron';
import path from 'path';
import { browserWindow } from '.';

let mainWindow;
let notification;
let userId = null;
let store;
let notificationListOfUsers={};
let channelWindow;

export function setNotificationConstants(_userId, _store, _channelWindow, _mainWindow){
    userId = _userId;
    store = _store;
    channelWindow = _channelWindow;
    mainWindow = _mainWindow;
    if(store.has('notificationListOfUsers')){
        notificationListOfUsers = store.get('notificationListOfUsers');
    }
    if( !notificationListOfUsers[userId] ) notificationListOfUsers[userId] = [];
    console.log("Users Notification bucket", notificationListOfUsers[userId].length);
}

function decodeHtmlEntities(html) {
    return html.replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");
}

export function markAsReadNotification(newNotification){
    let notificationArray = [];
    if(newNotification)
    notificationArray = newNotification;
    if(store.has('notificationListOfUsers')){
        console.log('markAsReadNotification')
        const _notificationListOfUsers = store.get('notificationListOfUsers');
        console.log('markAsReadNotification:length>>>',_notificationListOfUsers[userId]?.length);
        if(_notificationListOfUsers[userId]?.length > 0){
            notificationArray = notificationArray.concat(_notificationListOfUsers[userId]);
        }
    }
    if(notificationArray.length > 0)
    mainWindow?.webContents.send(channelWindow.markAsReadNotification, JSON.stringify(notificationArray));
}

export function getNotificationList(){
    if(userId)
        return notificationListOfUsers[userId];
    return [];
} 

export function showNotification(notificationSchema) {
    const currentIsDark = nativeTheme.shouldUseDarkColors;
    const iconPath = path.join(__dirname, '..', '..', 'public',currentIsDark?'asset/notification-icon-win-os-dark.png':'asset/notification-icon-win-os-light.png');
    const isDuplicateNotification = notificationListOfUsers[userId].some((_notification) => _notification.notificationId === notificationSchema.notificationId);
    if(!isDuplicateNotification){
        let notification;
        console.log("notificationSchema",notificationSchema)
        if(notificationSchema?.event === "NOTIFICATION_CHAT_USER_MESSAGE_RECEIVED"){
            if(process.platform === 'win32') notification = createChatWindowNotification(notificationSchema, iconPath);
            else notification = createChatMacNotification(notificationSchema, iconPath);
        }else if(notificationSchema?.event === "NOTIFICATION_CHAT_MODERATOR_MESSAGE_RECEIVED"){
            if(process.platform === 'win32') notification = createChatWindowNotification(notificationSchema, iconPath);
            else notification = createChatMacNotification(notificationSchema, iconPath);
        }else{
            if(process.platform === 'win32') notification = createWindowsNotification(notificationSchema, iconPath);
            else notification = createMacNotification(notificationSchema, iconPath);
        }
        notification.notificationId = notificationSchema.notificationId;
        notification.show();
        notificationListOfUsers[userId].push(notification);
    }
}

const createChatWindowNotification = (notificationSchema, iconPath)=>{
    let extractedBody = notificationSchema.body;
    const parsedBody = JSON.parse(notificationSchema.body);
    extractedBody = parsedBody.text || '';
    const currentIsDark = nativeTheme.shouldUseDarkColors;
    iconPath = path.join(__dirname, 'public','asset',currentIsDark?'notification-icon-win-os-dark.png':'notification-icon-win-os-light.png');
    const notification = new Notification({
        appId: 'com.squirrel.bryzos_extended_pricing_widget.bryzos',
        toastXml: `<toast activationType="protocol"  launch="bryzos://${notificationSchema.navigation.routePath};notificationId=${notificationSchema.notificationId}">
        <visual>
          <binding template="ToastImageAndText02">
            <image id="1" src="file://${iconPath}" alt="Image" />
            <text id="1">${notificationSchema.title}</text>
            <text id="2">${extractedBody}</text>
          </binding>
        </visual>
        <actions>
          <action
            content="Reply"
            arguments="bryzos://${notificationSchema.navigation.routePath};notificationId=${notificationSchema.notificationId}"
            activationType="protocol"/>
            <action
            content="Cancel"
            arguments="bryzos://cancel"
            activationType="protocol"/>
        </actions>
      </toast>`
    })
    return notification;
}

const createChatMacNotification = (notificationSchema, iconPath)=> {
    let extractedBody = notificationSchema.body;
    const parsedBody = JSON.parse(notificationSchema.body);
    extractedBody = parsedBody.text || '';
    const notification = new Notification({
        title: notificationSchema.title,
        body: extractedBody,
        actions: [
          { type: 'button', text: 'Reply' }
        ],
        closeButtonText: 'Close',
        silent:false,
        timeoutType:'default',
        urgency:'normal',
        icon:iconPath
      });
    
      // When a button is clicked
      notification.on('action', (event, index) => {
        if (index === 0) {
          // Reply button
            const url = `bryzos://${notificationSchema.navigation.routePath};notificationId=${notificationSchema.notificationId}`
            browserWindow.webContents.send(channelWindow.handleURI, url);
        }
      });
    
      return notification;
}

const createActionUrl = (navigationSchema, notificatonId) => {
    let actionUrl = `bryzos://${userId}/${notificatonId}/`;
    if(navigationSchema){
        if(navigationSchema.routePath)actionUrl+=`${navigationSchema.routePath}`;
        const state = navigationSchema.state;
        if(state){
            let stateUrl = '/';
            for(const key in state){
                stateUrl+=`${key}=${state[key]},`;
            }
            actionUrl+=`${stateUrl.substring(0,stateUrl.length-1)}`;
        }
    }
    return actionUrl;
}

const createWindowsNotification = (notificationSchema, iconPath) => {
    const title = notificationSchema.title;
    const message = notificationSchema.body;
    const navigationSchema = notificationSchema.navigation;
    const actionUrl = createActionUrl(navigationSchema, notificationSchema.notificationId);
    const currentIsDark = nativeTheme.shouldUseDarkColors;
    iconPath = path.join(__dirname, 'public','asset',currentIsDark?'notification-icon-win-os-dark.png':'notification-icon-win-os-light.png');
    notification = new Notification({
        appId: 'com.squirrel.bryzos_extended_pricing_widget.bryzos',
        toastXml: `<toast launch="${actionUrl}" activationType="protocol">
           <visual>
             <binding template="ToastImageAndText02">
                <image id="1" src="file://${iconPath}" alt="Image" />
               <text id="1">${title}</text>
               <text id="2">${message}</text>
             </binding>
           </visual>
        </toast>`,
    });
    return notification;
}

const createMacNotification = (notificationSchema, iconPath)=> {
    const title = decodeHtmlEntities(notificationSchema.title);
    const body = decodeHtmlEntities(notificationSchema.body);
    const navigationSchema = notificationSchema.navigation;
    const actionUrl = createActionUrl(navigationSchema, notificationSchema.notificationId);
    notification = new Notification({
        title,
        body,
        icon: iconPath,
      });
      
    notification.on('click', () => {
        if (mainWindow) {
            mainWindow.webContents.send(channelWindow.handleURI, actionUrl);
            if (mainWindow.isMinimized()) mainWindow.restore();
            if(!mainWindow.isVisible()){
                mainWindow.show();
            }
        }
    });
    return notification;
}

export const notificationHandler = (notification) => {
    showNotification(notification);
    mainWindow?.webContents.send(channelWindow.markAsReadNotification, JSON.stringify([notification]));
}

export const notificationEventsHandler = (channel, channelEvents) => {
    channelEvents.forEach($event => {
        channel.bind( $event, (data) => {
            notificationHandler(data.notification);
        });
    });
}